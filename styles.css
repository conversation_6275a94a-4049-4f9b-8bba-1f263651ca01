
:root {
    --primary: #1d4ed8;
    --accent: #0ea5e9;
    --secondary: #3b82f6;
    --bg: #000000;
    --text: #000000;
    --glass: rgba(255, 255, 255, 0.1);
    --glass-border: rgb(154 154 154 / 20%);
}
body.dark:root {
    --primary: #1d4ed8;
    --accent: #0ea5e9;
    --secondary: #3b82f6;
    --bg: #000000;
    --text: #ffffff;
    --glass: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
}

* {
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: var(--bg);
    color: var(--text);
    overflow-x: hidden;
}

/* Enhanced Glassmorphism */
body.dark .glass-card {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 20px 0px rgb(92 92 92 / 5%);
    border: 1px solid var(--glass-border);
}

/* Enhanced Neon Effects */
.neon-btn {
    background: linear-gradient(135deg, var(--primary), var(--accent));
    box-shadow: 0 0 20px rgba(29, 78, 216, 0.5);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    color: white;
}

.neon-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.neon-btn:hover::before {
    left: 100%;
}

.neon-btn:hover {
    box-shadow: 0 0 40px rgba(29, 78, 216, 0.8), 0 0 60px rgba(14, 165, 233, 0.4);
    transform: translateY(-3px) scale(1.02);
}

/* Advanced Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-15px) rotate(1deg); }
    66% { transform: translateY(-5px) rotate(-1deg); }
}

@keyframes pulse-glow {
    0%, 100% { 
        box-shadow: 0 0 20px rgba(29, 78, 216, 0.5);
        transform: scale(1);
    }
    50% { 
        box-shadow: 0 0 40px rgba(14, 165, 233, 0.8), 0 0 60px rgba(29, 78, 216, 0.3);
        transform: scale(1.05);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.floating {
    animation: float 8s ease-in-out infinite;
}

.pulse-glow {
    animation: pulse-glow 3s ease-in-out infinite;
}

.animate-slide-up {
    animation: slideInUp 0.8s ease-out forwards;
}

.animate-slide-right {
    animation: slideInRight 0.8s ease-out forwards;
}

.animate-slide-left {
    animation: slideInLeft 0.8s ease-out forwards;
}

/* Hero Background */
.Overlay{
  background-color: #ffffff;
  opacity: 0.6;
}
.dark .Overlay{
  background-color: #000000;
  opacity: 0.6;
}
.hero-bg {
    background-image: url('/images/BG/bg-hero-light.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
.dark .hero-bg {
  background-image: url('/images/BG/bg-hero-dark.png');
}
/* Enhanced Scrollbar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: #111;
    border-radius: 5px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(var(--primary), var(--accent));
    border-radius: 5px;
    border: 2px solid #111;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(var(--accent), var(--secondary));
}

/* Product Cards */
.product-card {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(29, 78, 216, 0.3);
}

/* Enhanced Typography */
.gradient-text {
    background: linear-gradient(135deg, var(--primary), var(--accent), var(--secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}


/* Loading Animation */
.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { content: ''; }
    25% { content: '.'; }
    50% { content: '..'; }
    75% { content: '...'; }
    100% { content: ''; }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .hero-bg {
        background-position: center top;
    }
}


.section-header {
  position: relative;
  display: inline-block;
  background: linear-gradient(135deg, var(--primary), var(--accent));
  color: white;
  font-weight: bold;
  border: 2px solid transparent;
  transition: background 0.4s, color 0.4s, border-color 0.4s;
  cursor: pointer;
}

.section-header .line-left,
.section-header .line-right {
  content: '';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 100%;
  background: var(--primary);
  transition: all 0.4s ease;
  z-index: 2;
  pointer-events: none;
}

.section-header .line-left {
  left: -15px;
}

.section-header .line-right {
  right: -15px;
}

/* عندما نحوم */
.section-header:hover {
  background: transparent;
  color: var(--primary);
  border-color: var(--primary);
}

.section-header:hover .line-left {
  left: 0.5em;
  opacity: 0;
}

.section-header:hover .line-right {
  right: 0.5em;
  opacity: 0;
}

.section-header span.text {
  position: relative;
  z-index: 3;
}


/* :root {
  --color-primary: #1E3A8A;
  --color-secondary: #000000;
  --color-accent: #FFFFFF;
}
.theme-blue {
  --color-primary: #1E3A8A;
  --color-secondary: #000000;
  --color-accent: #FFFFFF;
}
.theme-dark {
  --color-primary: #0f172a;
  --color-secondary: #000000;
  --color-accent: #e5e7eb;
}
.theme-light {
  --color-primary: #FFFFFF;
  --color-secondary: #1E3A8A;
  --color-accent: #1E3A8A;
}  */